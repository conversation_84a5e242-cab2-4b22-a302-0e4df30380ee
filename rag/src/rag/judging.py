from fast_service import RequestContext
from langchain_core.prompts import Chat<PERSON>romptTemplate
from .shared import rag_fsm
from .shared import JudgingOutput
from .shared import RetrievalJudgingInput
from .shared import DocRelevanceJudgingInput
from .shared import HallucinationJudgingInput
from .shared import AnswerJudgingInput
from .utils import get_llm

DEFAULT_RETRIEVAL_JUDGING_TEMPLATE = """You are a grader assessing whether an retrieval for relevant documents is necessary to answer the user question. \n
If you do not have the necessary information to answer the question, you may need to retrieve relevant documents. \n
Give a binary score 'yes' or 'no'. 'yes' means that the retrieval is necessary to answer the user question. \n
User question: {question}
"""


@rag_fsm.fast_service
def retrieval_judger(
    item: RetrievalJudgingInput, context: RequestContext = None
) -> JudgingOutput:
    question = item.question
    llm = get_llm()
    prompt_template = DEFAULT_RETRIEVAL_JUDGING_TEMPLATE
    prompt = ChatPromptTemplate.from_template(template=prompt_template)
    llm_input = prompt.invoke(
        input={
            "question": question,
        }
    )
    llm_output = llm.invoke(input=llm_input)
    answer = llm_output.content
    return JudgingOutput(judgement="yes" in answer.lower())


# reference: https://github.com/langchain-ai/langgraph/blob/main/examples/rag/langgraph_adaptive_rag_cohere.ipynb
DEFAULT_DOC_RELEVANCE_JUDGING_TEMPLATE = (
    """You are a grader assessing relevance of a retrieved document to a user question. \n 
        Here is the retrieved document: \n\n {context} \n\n
        Here is the user question: {question} \n
        If the document contains keyword(s) or semantic meaning related to the user question, grade it as relevant. \n
        Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question.""",
)


@rag_fsm.fast_service
def doc_relevance_judger(
    item: DocRelevanceJudgingInput, context: RequestContext = None
) -> JudgingOutput:
    question = item.question
    q_ctx = item.context
    llm = get_llm()
    prompt_template = DEFAULT_DOC_RELEVANCE_JUDGING_TEMPLATE
    prompt = ChatPromptTemplate.from_template(template=prompt_template)
    llm_input = prompt.invoke(
        input={
            "context": q_ctx,
            "question": question,
        }
    )
    llm_output = llm.invoke(input=llm_input)
    answer = llm_output.content
    return JudgingOutput(judgement="yes" in answer.lower())


DEFAULT_HALLUCINATION_JUDGING_TEMPLATE = """You are a grader assessing whether an LLM generation is grounded in / supported by a set of retrieved facts. \n
Give a binary score 'yes' or 'no'. 'yes' means that the answer is grounded in / supported by the set of facts. \n
Set of facts: {context} \n\n LLM generation: {answer}
"""


@rag_fsm.fast_service
def hallucination_judger(
    item: HallucinationJudgingInput, context: RequestContext = None
) -> JudgingOutput:
    answer = item.answer
    q_ctx = item.context
    llm = get_llm()
    prompt_template = DEFAULT_HALLUCINATION_JUDGING_TEMPLATE
    prompt = ChatPromptTemplate.from_template(template=prompt_template)
    llm_input = prompt.invoke(
        input={
            "context": q_ctx,
            "answer": answer,
        }
    )
    llm_output = llm.invoke(input=llm_input)
    answer = llm_output.content
    return JudgingOutput(judgement="yes" in answer.lower())


DEFAULT_ANSWER_JUDGING_TEMPLATE = """You are a grader assessing whether an answer addresses / resolves a question \n
Give a binary score 'yes' or 'no'. 'yes' means that the answer resolves the question. \n
"User question: \n\n {question} \n\n LLM generation: {answer}"
"""


@rag_fsm.fast_service
def answer_judger(
    item: AnswerJudgingInput, context: RequestContext = None
) -> JudgingOutput:
    question = item.question
    answer = item.answer
    llm = get_llm()
    prompt_template = DEFAULT_ANSWER_JUDGING_TEMPLATE
    prompt = ChatPromptTemplate.from_template(template=prompt_template)
    llm_input = prompt.invoke(
        input={
            "question": question,
            "answer": answer,
        }
    )
    llm_output = llm.invoke(input=llm_input)
    answer = llm_output.content
    return JudgingOutput(judgement="yes" in answer.lower())
